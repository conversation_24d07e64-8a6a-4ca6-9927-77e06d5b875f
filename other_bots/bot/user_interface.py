"""
User Interface for multi-user trading bot.
Handles user interactions via Telegram or dashboard.
"""


class UserInterface:
    def __init__(self, user_manager):
        self.user_manager = user_manager

    def handle_registration(self, user_id, api_key, secret_key):
        """Register a user through the interface."""
        self.user_manager.register_user(user_id, api_key, secret_key)
        return f"User {user_id} registered successfully."

    def authenticate(self, user_id):
        """Authenticate a user."""
        if self.user_manager.authenticate_user(user_id):
            return f"User {user_id} authenticated."
        return f"User {user_id} not found."
