#!/usr/bin/env python3
"""
User Management System voor Crypto Trading Bot
Handles users, roles, permissions, and audit logging
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
import logging

class UserManager:
    def __init__(self, db_path="crypto_bot.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                telegram_id BIGINT UNIQUE NOT NULL,
                username VA<PERSON>HA<PERSON>(50),
                first_name VA<PERSON>HA<PERSON>(100),
                last_name VA<PERSON><PERSON><PERSON>(100),
                role VARCHAR(20) DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                trading_enabled BOOLEAN DEFAULT 1,
                daily_limit DECIMAL(10,2) DEFAULT 100.00,
                total_traded DECIMAL(10,2) DEFAULT 0.00
            )
        ''')

        # Roles table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100),
                description TEXT,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')

        # Audit logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(100) NOT NULL,
                resource VARCHAR(100),
                details TEXT,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')

        # Trading activities table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action_type VARCHAR(50),
                crypto_symbol VARCHAR(10),
                amount DECIMAL(18, 8),
                price DECIMAL(18, 8),
                exchange VARCHAR(50),
                order_id VARCHAR(100),
                status VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')

        # Insert default roles
        self._insert_default_roles(cursor)

        conn.commit()
        conn.close()
        logging.info("Database initialized successfully")

    def _insert_default_roles(self, cursor):
        """Insert default roles with permissions"""
        roles = [
            ('super_admin', 'Super Administrator', 'Full system control',
             '["all_permissions", "admin_panel", "manage_users", "make_admin", "system_settings", "emergency_stop"]'),

            ('admin', 'Administrator', 'Bot management and trading oversight',
             '["admin_panel", "manage_users", "view_stats", "trading_oversight", "user_support"]'),

            ('ceo', 'CEO Dashboard', 'Executive dashboard access',
             '["ceo_dashboard", "view_analytics", "view_reports", "system_overview"]'),

            ('trader', 'Advanced Trader', 'Enhanced trading features',
             '["advanced_trading", "higher_limits", "market_analysis", "portfolio_management"]'),

            ('user', 'Regular User', 'Basic trading rights',
             '["basic_trading", "view_portfolio", "view_prices", "market_data"]'),

            ('viewer', 'Viewer Only', 'Read-only access',
             '["view_prices", "view_public_data"]')
        ]

        for role in roles:
            cursor.execute('''
                INSERT OR IGNORE INTO roles (name, display_name, description, permissions)
                VALUES (?, ?, ?, ?)
            ''', role)

    def add_user(self, telegram_id, username=None, first_name=None, last_name=None, role='user'):
        """Add new user to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT OR REPLACE INTO users
                (telegram_id, username, first_name, last_name, role, last_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (telegram_id, username, first_name, last_name, role, datetime.now()))

            conn.commit()
            user_id = cursor.lastrowid

            # Log the action
            self.log_action(user_id, "user_registered", "users",
                          f"User {username or first_name} registered with role {role}")

            conn.close()
            return True

        except Exception as e:
            logging.error(f"Error adding user: {e}")
            conn.close()
            return False

    def get_user(self, telegram_id):
        """Get user by telegram ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, telegram_id, username, first_name, last_name, role,
                   created_at, updated_at, last_active, is_active, trading_enabled,
                   daily_limit, total_traded
            FROM users WHERE telegram_id = ?
        ''', (telegram_id,))

        result = cursor.fetchone()

        if result:
            # Update last active
            cursor.execute('''
                UPDATE users SET last_active = ? WHERE telegram_id = ?
            ''', (datetime.now(), telegram_id))
            conn.commit()

            # Convert to dictionary
            user_data = {
                'id': result[0],
                'telegram_id': result[1],
                'username': result[2],
                'first_name': result[3],
                'last_name': result[4],
                'role': result[5],
                'created_at': result[6],
                'updated_at': result[7],
                'last_active': result[8],
                'is_active': bool(result[9]),
                'trading_enabled': bool(result[10]),
                'daily_limit': float(result[11]),
                'total_traded': float(result[12])
            }

            conn.close()
            return user_data

        conn.close()
        return None

    def update_user_role(self, telegram_id, new_role):
        """Update user role"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE users SET role = ?, updated_at = ?
                WHERE telegram_id = ?
            ''', (new_role, datetime.now(), telegram_id))

            conn.commit()

            # Log the action
            user = self.get_user(telegram_id)
            if user:
                self.log_action(user['id'], "role_updated", "users",
                              f"Role changed to {new_role}")

            conn.close()
            return True

        except Exception as e:
            logging.error(f"Error updating user role: {e}")
            conn.close()
            return False

    def get_user_permissions(self, telegram_id):
        """Get user permissions based on role"""
        user = self.get_user(telegram_id)
        if not user:
            return []

        role = user['role']

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT permissions FROM roles WHERE name = ?', (role,))
        result = cursor.fetchone()

        conn.close()

        if result and result[0]:
            try:
                return json.loads(result[0])
            except:
                return []
        return []

    def has_permission(self, telegram_id, permission):
        """Check if user has specific permission"""
        permissions = self.get_user_permissions(telegram_id)
        return "all_permissions" in permissions or permission in permissions

    def get_all_users(self, limit=100):
        """Get all users with pagination"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT telegram_id, username, first_name, last_name, role,
                   created_at, last_active, is_active, trading_enabled
            FROM users
            ORDER BY created_at DESC
            LIMIT ?
        ''', (limit,))

        users = cursor.fetchall()
        conn.close()

        return [
            {
                'telegram_id': user[0],
                'username': user[1],
                'first_name': user[2],
                'last_name': user[3],
                'role': user[4],
                'created_at': user[5],
                'last_active': user[6],
                'is_active': bool(user[7]),
                'trading_enabled': bool(user[8])
            }
            for user in users
        ]

    def get_stats(self):
        """Get system statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Total users
        cursor.execute('SELECT COUNT(*) FROM users')
        total_users = cursor.fetchone()[0]

        # Active users (last 24h)
        yesterday = datetime.now() - timedelta(days=1)
        cursor.execute('SELECT COUNT(*) FROM users WHERE last_active > ?', (yesterday,))
        active_users = cursor.fetchone()[0]

        # Role distribution
        cursor.execute('SELECT role, COUNT(*) FROM users GROUP BY role')
        role_dist = dict(cursor.fetchall())

        # Trading activities (last 24h)
        cursor.execute('SELECT COUNT(*) FROM trading_activities WHERE created_at > ?', (yesterday,))
        trading_activities = cursor.fetchone()[0]

        conn.close()

        return {
            'total_users': total_users,
            'active_users': active_users,
            'role_distribution': role_dist,
            'trading_activities_24h': trading_activities
        }

    def log_action(self, user_id, action, resource, details):
        """Log user action for audit trail"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO audit_logs (user_id, action, resource, details)
            VALUES (?, ?, ?, ?)
        ''', (user_id, action, resource, details))

        conn.commit()
        conn.close()

    def get_audit_logs(self, limit=50):
        """Get recent audit logs"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT al.*, u.username, u.first_name
            FROM audit_logs al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.created_at DESC
            LIMIT ?
        ''', (limit,))

        logs = cursor.fetchall()
        conn.close()

        return [
            {
                'id': log[0],
                'user_id': log[1],
                'action': log[2],
                'resource': log[3],
                'details': log[4],
                'created_at': log[6],
                'username': log[7],
                'first_name': log[8]
            }
            for log in logs
        ]

    def ban_user(self, telegram_id, reason=""):
        """Ban user (disable account)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE users SET is_active = 0, trading_enabled = 0, updated_at = ?
                WHERE telegram_id = ?
            ''', (datetime.now(), telegram_id))

            conn.commit()

            # Log the action
            user = self.get_user(telegram_id)
            if user:
                self.log_action(user['id'], "user_banned", "users",
                              f"User banned. Reason: {reason}")

            conn.close()
            return True

        except Exception as e:
            logging.error(f"Error banning user: {e}")
            conn.close()
            return False

    def unban_user(self, telegram_id):
        """Unban user (re-enable account)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE users SET is_active = 1, trading_enabled = 1, updated_at = ?
                WHERE telegram_id = ?
            ''', (datetime.now(), telegram_id))

            conn.commit()

            # Log the action
            user = self.get_user(telegram_id)
            if user:
                self.log_action(user['id'], "user_unbanned", "users", "User unbanned")

            conn.close()
            return True

        except Exception as e:
            logging.error(f"Error unbanning user: {e}")
            conn.close()
            return False
