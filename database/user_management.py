#!/usr/bin/env python3
"""
User Management Database System
Handles users, roles, permissions, and admin functions
"""

import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os


class UserManager:
    def __init__(self, db_path="trading_bot.db"):
        self.db_path = db_path
        self.init_database()
        self.init_default_roles()

    def init_database(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                telegram_id BIGINT UNIQUE NOT NULL,
                username VA<PERSON>HA<PERSON>(50),
                first_name VA<PERSON><PERSON><PERSON>(100),
                last_name VA<PERSON><PERSON><PERSON>(100),
                role VARCHAR(20) DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT true,
                settings TEXT DEFAULT '{}',
                balance_usd DECIMAL(10,2) DEFAULT 0.00
            )
        """)

        # Roles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) UNIQUE NOT NULL,
                permissions TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # User activity log
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(100),
                details TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Trading permissions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trading_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                max_trade_amount DECIMAL(10,2) DEFAULT 100.00,
                daily_limit DECIMAL(10,2) DEFAULT 500.00,
                can_trade BOOLEAN DEFAULT false,
                can_withdraw BOOLEAN DEFAULT false,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        conn.commit()
        conn.close()
        print("✅ Database tables created successfully")

    def init_default_roles(self):
        """Initialize default roles and permissions"""
        default_roles = [
            {
                "name": "super_admin",
                "permissions": {
                    "admin_panel": True,
                    "manage_users": True,
                    "make_admin": True,
                    "view_all_stats": True,
                    "system_settings": True,
                    "emergency_stop": True,
                    "unlimited_trading": True,
                },
                "description": "Full system access",
            },
            {
                "name": "admin",
                "permissions": {
                    "admin_panel": True,
                    "manage_users": True,
                    "view_stats": True,
                    "moderate_users": True,
                    "trading_oversight": True,
                },
                "description": "Administrative access",
            },
            {
                "name": "premium_user",
                "permissions": {
                    "advanced_trading": True,
                    "higher_limits": True,
                    "priority_support": True,
                    "advanced_analytics": True,
                },
                "description": "Premium user features",
            },
            {
                "name": "user",
                "permissions": {"basic_trading": True, "view_portfolio": True, "basic_analytics": True},
                "description": "Standard user access",
            },
        ]

        for role in default_roles:
            self.create_role(role["name"], role["permissions"], role["description"])

    def create_role(self, name: str, permissions: Dict, description: str = ""):
        """Create a new role"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                INSERT OR IGNORE INTO roles (name, permissions, description)
                VALUES (?, ?, ?)
            """,
                (name, json.dumps(permissions), description),
            )
            conn.commit()
        except Exception as e:
            print(f"Error creating role {name}: {e}")
        finally:
            conn.close()

    def add_user(
        self, telegram_id: int, username: str = None, first_name: str = None, last_name: str = None, role: str = "user"
    ) -> bool:
        """Add a new user to the system"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                INSERT OR REPLACE INTO users
                (telegram_id, username, first_name, last_name, role, last_active)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (telegram_id, username, first_name, last_name, role, datetime.now()),
            )

            user_id = cursor.lastrowid

            # Create default trading permissions
            cursor.execute(
                """
                INSERT OR IGNORE INTO trading_permissions (user_id)
                VALUES (?)
            """,
                (user_id,),
            )

            conn.commit()
            self.log_activity(user_id, "user_registered", f"New user registered: {username}")
            return True

        except Exception as e:
            print(f"Error adding user: {e}")
            return False
        finally:
            conn.close()

    def get_user(self, telegram_id: int) -> Optional[Dict]:
        """Get user information"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT u.*, tp.max_trade_amount, tp.daily_limit, tp.can_trade, tp.can_withdraw
            FROM users u
            LEFT JOIN trading_permissions tp ON u.id = tp.user_id
            WHERE u.telegram_id = ?
        """,
            (telegram_id,),
        )

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                "id": row[0],
                "telegram_id": row[1],
                "username": row[2] or "Unknown",
                "first_name": row[3] or "Unknown",
                "last_name": row[4] or "Unknown",
                "role": row[5] or "user",
                "max_trade_amount": row[6] or 0,
                "daily_limit": row[7] or 0,
                "can_trade": row[8] if row[8] is not None else False,
                "can_withdraw": row[9] if row[9] is not None else False,
            }
        return None

    def update_user_role(self, telegram_id: int, new_role: str) -> bool:
        """Update user role"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                UPDATE users SET role = ? WHERE telegram_id = ?
            """,
                (new_role, telegram_id),
            )
            conn.commit()

            user = self.get_user(telegram_id)
            if user:
                self.log_activity(user["id"], "role_changed", f"Role changed to: {new_role}")
            return True

        except Exception as e:
            print(f"Error updating user role: {e}")
            return False
        finally:
            conn.close()

    def get_user_permissions(self, telegram_id: int) -> Dict:
        """Get user permissions based on role"""
        user = self.get_user(telegram_id)
        if not user:
            return {}

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT permissions FROM roles WHERE name = ?
        """,
            (user["role"],),
        )

        row = cursor.fetchone()
        conn.close()

        if row:
            return json.loads(row[0])
        return {}

    def has_permission(self, telegram_id: int, permission: str) -> bool:
        """Check if user has specific permission"""
        permissions = self.get_user_permissions(telegram_id)
        return permissions.get(permission, False)

    def log_activity(self, user_id: int, action: str, details: str = ""):
        """Log user activity"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                INSERT INTO user_activity (user_id, action, details)
                VALUES (?, ?, ?)
            """,
                (user_id, action, details),
            )
            conn.commit()
        except Exception as e:
            print(f"Error logging activity: {e}")
        finally:
            conn.close()

    def get_all_users(self) -> List[Dict]:
        """Get all users for admin panel"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT telegram_id, username, first_name, role, created_at, is_active, last_active
            FROM users
            ORDER BY created_at DESC
        """)

        rows = cursor.fetchall()
        conn.close()

        users = []
        for row in rows:
            users.append(
                {
                    "telegram_id": row[0],
                    "username": row[1],
                    "first_name": row[2],
                    "role": row[3],
                    "created_at": row[4],
                    "is_active": row[5],
                    "last_active": row[6],
                }
            )

        return users

    def get_stats(self) -> Dict:
        """Get system statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Total users
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]

        # Active users (last 24 hours)
        cursor.execute("""
            SELECT COUNT(*) FROM users
            WHERE last_active > datetime('now', '-1 day')
        """)
        active_users = cursor.fetchone()[0]

        # Users by role
        cursor.execute("""
            SELECT role, COUNT(*) FROM users GROUP BY role
        """)
        role_stats = dict(cursor.fetchall())

        conn.close()

        return {"total_users": total_users, "active_users": active_users, "role_distribution": role_stats}
