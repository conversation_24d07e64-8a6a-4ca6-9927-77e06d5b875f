"""
Database Manager for user_data.db
Handles adding and retrieving data from the users and preferences tables.
"""

import sqlite3


class DatabaseManager:
    def __init__(self, db_path):
        self.db_path = db_path

    def connect(self):
        """Connect to the SQLite database."""
        return sqlite3.connect(self.db_path)

    def create_tables(self):
        """Create users and roles tables."""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    telegram_id BIGINT UNIQUE,
                    username VARCHAR(50),
                    role VARCHAR(20) DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT true
                )
                """
            )
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS roles (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(50) UNIQUE,
                    permissions TEXT
                )
                """
            )
            conn.commit()

    def add_user(self, user_id, api_key, secret_key):
        """Add a new user to the users table."""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO users (user_id, api_key, secret_key) VALUES (?, ?, ?)", (user_id, api_key, secret_key)
            )
            conn.commit()

    def add_preferences(self, user_id, risk_level, trade_frequency):
        """Add preferences for a user to the preferences table."""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO preferences (user_id, risk_level, trade_frequency) VALUES (?, ?, ?)",
                (user_id, risk_level, trade_frequency),
            )
            conn.commit()

    def get_users(self):
        """Retrieve all users from the users table."""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users")
            return cursor.fetchall()

    def get_preferences(self):
        """Retrieve all preferences from the preferences table."""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM preferences")
            return cursor.fetchall()


# Example usage
if __name__ == "__main__":
    db_manager = DatabaseManager("user_data.db")

    # Create tables
    db_manager.create_tables()

    # Add example data
    db_manager.add_user("user1", "example_api_key", "example_secret_key")
    db_manager.add_preferences("user1", "medium", "daily")

    # Retrieve and print data
    print("Users:", db_manager.get_users())
    print("Preferences:", db_manager.get_preferences())
